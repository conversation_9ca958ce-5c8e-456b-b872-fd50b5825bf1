import { create<PERSON>pi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { NurseRegistrationData } from '@/types/auth';

const baseUrl =
  import.meta.env.VITE_API_BASE_URL ||
  'http://localhost:8000/nurseApi';

interface OTPVerificationData {
  username: string;
  confirmationCode: string;
}

interface Forgot_Password {
  phone_number: string;
}

interface ResetPasswordData {
  phone_number: string;
  confirmationCode: string;
  newPassword: string;
}

interface ChangePassword {
  phone_number: string;
  old_password: string;
  new_password: string;
}

interface ResendOTP {
  phone_number: string;
}

interface NurseLocationData {
  latitude: number;
  longitude: number;
  address: string;
}

interface PhoneLoginData {
  phone_number: string;
  password: string;
}

interface GetProfileData {
  username: string;
}

interface PersonalDetailsData {
  nuid: string;
  service_provide: string[];
  total_years_of_exp: number;
  selected_services: string[];
  custom_service?: string;
  date_of_birth: Date;
  gender: string;
  emergency_contact: string | '';
  nurse_onboard_complete: boolean;
}

interface NursePersonalDetailsData {
  date_of_birth: string;
  gender: string;
  given_name: string;
  family_name: string;
  email: string;
}

interface NurseProfessionalDetailsData {
  total_years_of_exp: number;
  emergency_contact: string | '';
  about: string | '';
  service_provide: string[];
}

interface ProfileDetails {
  id: number;
  user_id: string;
  date_of_birth: string;
  gender: string;
  emergency_contact: string | '';
  total_years_of_exp: number;
  service_provide: string[];
  created_at: string;
  updated_at: string;
  phone_number: string;
  given_name: string;
  family_name: string;
  email: string;
  longitude: number | '';
  latitude: number | '';
  address: string | '';
  about: string | '';
  profile_verified?: boolean;
}

interface ProfileDetailsResponse {
  details: ProfileDetails;
}

export interface Document {
  id: number;
  user_id: string;
  file_name: string;
  original_name: string;
  s3_key: string;
  s3_url: string;
  content_type: string;
  size: number;
  description?: string;
  document_type?: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

export interface DocumentListResponse {
  documents: Document[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface DocumentResponse {
  document: Document;
  signed_url: string;
}

export interface UploadDocumentResponse {
  message: string;
  document: Document;
  signed_url: string;
}

export interface DocumentUrlResponse {
  url: string;
}

export interface UpdateDocumentData {
  description?: string;
  document_type?: string;
  is_public?: boolean;
}

export interface ListDocumentsParams {
  page?: number;
  limit?: number;
  document_type?: string;
}

export interface AuthResponse {
  success: boolean;
  message?: string;
  tokens?: {
    accessToken: string;
    idToken?: string;
    refreshToken?: string;
  };
  user?: {
    id: string;
    email: string;
    given_name: string;
    middle_name?: string;
    family_name: string;
    username: string;
    nurse_onboard_complete: boolean;
    nurse_set_location: boolean;
    address: string;
    latitude: number;
    longitude: number;
    profile_verified?: boolean;
  };
}

interface ProfileResponse {
  nurse_set_location: boolean;
  nurse_onboard_complete: boolean;
  success: boolean;
  data?: {
    nurse_onboard_complete: boolean;
    nurse_set_location: boolean;
    address?: string;
    given_name?: string;
    profile_verified?: boolean;
  };
}

interface ServiceProvider {
  id: string;
  service_name: string;
}

interface TimeSlots {
  id: string;
  time_slot: string;
}

export interface TimeSlot {
  id: string;
  time_slot: string;
  display_time: string;
  sort_order: number;
}

interface ServiceProvidersResponse {
  success: boolean;
  data?: ServiceProvider[];
}

interface TimeSlotsResponse {
  success: boolean;
  data?: TimeSlots[];
}

interface Feedback {
  id: string;
  feedbackId: string;
  recipientName: string;
  name: string;
  rating: number;
  comments: string;
  submittedAt: string;
  createdAt: string;
  updatedAt: string;
}

interface GetFeedbackResponse {
  success?: boolean;
  feedback: Feedback[];
  rating_stats?: RatingStats;
  message?: string;
}

interface RatingStats {
  averageRating: number;
  totalFeedbacks: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

export interface AvailabilitySlot {
  id?: number;
  nurse_id: string;
  availability_date: string;
  availability_slots: string[];
  hourly_fare: number;
  created_at?: string;
  updated_at?: string;
}

export interface CreateAvailabilityData {
  availability_date: string;
  availability_timeSlots: string[];
  hourly_fare: number;
}

export interface CreateMultipleAvailabilitiesData {
  availability_slots: CreateAvailabilityData[];
}

export interface CreateMultipleAvailabilitiesResponse {
  message: string;
  summary: {
    total_requested: number;
    successful_dates: number;
    failed_dates: number;
  };
  successful: Array<{
    group_index: number;
    availability_date: string;
    availability_time_slots: string[];
    hourly_fare: number;
    action: 'created' | 'updated';
    created_availability?: AvailabilitySlot;
    updated_availability?: AvailabilitySlot;
  }>;
  failed: Array<{
    group_index: number;
    availability_date: string;
    availability_time_slots?: string[];
    hourly_fare?: number;
    error: string;
  }>;
}

export interface GetMyAvailabilitiesResponse {
  nurse_id: string;
  nurseName: string;
  availability_slots: Array<{
    id: number;
    availability_date: string;
    availability_slots: string[];
    hourly_fare: number;
  }>;
}

export interface UpdateAvailabilityData {
  availability_slot?: string;
  hourly_fare?: number;
}

export interface UpdateAvailabilityResponse {
  message: string;
  availability: AvailabilitySlot;
}

export interface DeleteAvailabilityResponse {
  message: string;
}

export interface GetNurseAvailabilityResponse {
  availability: AvailabilitySlot[];
  nurseId: string;
  nurseName: string;
}

export interface GetAllAvailabilitiesResponse {
  availabilities: AvailabilitySlot[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  message: string;
}

export interface GetAllAvailabilitiesParams {
  page?: number;
  limit?: number;
  date?: string;
  nurseId?: string;
}

export interface GetUserDetailsResponse {
  name: string;
  nurseId: string;
}

interface DeleteAccount {
  phone_number: string;
  password: string;
}

export interface ProfileImage {
  id: number;
  cognito_id: string;
  username: string;
  email: string;
  profile_image_name: string;
  s3_key: string;
  s3_url: string;
  signed_url: string;
  updated_at: string;
}

export interface ProfileImageResponse {
  message: string;
  profile_image: ProfileImage;
}

export interface ProfileImageUrlResponse {
  url: string;
}

export interface UploadProfileImageResponse {
  message: string;
  profile_image: {
    profile_image_name: string;
    s3_key: string;
    s3_url: string;
    signed_url: string;
    updated_at: string;
  };
}

export interface DeleteProfileImageResponse {
  message: string;
}

export interface BankDetailsData {
  account_holder_name: string;
  account_no: string;
  bank_name: string;
  branch: string;
  ifsc_code: string;
  address: string;
  status?: 'active' | 'inactive';
}

export interface BankDetailsUpdateData {
  account_holder_name?: string;
  account_no?: string;
  bank_name?: string;
  branch?: string;
  ifsc_code?: string;
  address?: string;
  status?: 'active' | 'inactive';
}

export interface BankDetailsResponse {
  success: boolean;
  message: string;
  data?: {
    encrypted: boolean;
    data: string;
  };
  errors?: Array<{
    type: string;
    value: string;
    msg: string;
    path: string;
    location: string;
  }>;
}

export interface DeleteBankDetailsResponse {
  success: boolean;
  message: string;
}

export const apiSlice = createApi({
  reducerPath: 'nurseApi',
  baseQuery: fetchBaseQuery({
    baseUrl,
    prepareHeaders: (headers, { getState: _getState }) => {
      const token = localStorage.getItem('idToken');
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: [
    'Feedback',
    'Document',
    'Availability',
    'ProfileImage',
    'BankDetails',
    'Profile',
  ],
  endpoints: builder => ({
    getExample: builder.query({
      query: () => 'example',
    }),

    registerNurse: builder.mutation<AuthResponse, NurseRegistrationData>({
      query: data => ({
        url: 'auth/register',
        method: 'POST',
        body: data,
      }),

      transformResponse: (response: AuthResponse) => {
        if (response?.tokens?.idToken) {
          localStorage.setItem('token', response.tokens.idToken);
        }
        return response;
      },
    }),

    verifyOTP: builder.mutation<AuthResponse, OTPVerificationData>({
      query: data => ({
        url: 'auth/confirm',
        method: 'POST',
        body: data,
      }),

      transformResponse: (response: AuthResponse) => {
        if (response?.tokens?.idToken) {
          localStorage.setItem('token', response.tokens.idToken);
        }
        return response;
      },
    }),

    forgotPassword: builder.mutation<AuthResponse, Forgot_Password>({
      query: data => ({
        url: 'auth/forgot-password',
        method: 'POST',
        body: data,
      }),
    }),

    changePassword: builder.mutation<AuthResponse, ChangePassword>({
      query: data => ({
        url: 'auth/change-password',
        method: 'POST',
        body: data,
      }),
    }),

    confirmForgotPassword: builder.mutation<AuthResponse, ResetPasswordData>({
      query: data => ({
        url: 'auth/confirm-forgot-password',
        method: 'POST',
        body: data,
      }),
    }),

    resendOTP: builder.mutation<AuthResponse, ResendOTP>({
      query: data => ({
        url: 'auth/resend-otp',
        method: 'POST',
        body: data,
      }),
    }),

    updateNurseLocation: builder.mutation<AuthResponse, NurseLocationData>({
      query: data => ({
        url: '/users/location',
        method: 'PUT',
        body: data,
      }),
      transformResponse: (response: AuthResponse) => {
        if (response?.tokens?.idToken) {
          localStorage.setItem('token', response.tokens.idToken);
        }
        return response;
      },
      invalidatesTags: ['Profile'],
    }),
    uploadDocument: builder.mutation<UploadDocumentResponse, FormData>({
      query: formData => ({
        url: 'documents/upload',
        method: 'POST',
        body: formData,
      }),
      invalidatesTags: ['Document'],
    }),

    listDocuments: builder.query<
      DocumentListResponse,
      ListDocumentsParams | void
    >({
      query: (params: ListDocumentsParams = {}) => {
        const searchParams = new URLSearchParams();
        if (params.page) searchParams.append('page', params.page.toString());
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.document_type)
          searchParams.append('document_type', params.document_type);

        return {
          url: `documents?${searchParams.toString()}`,
          method: 'GET',
        };
      },
      providesTags: ['Document'],
      keepUnusedDataFor: 300,
    }),

    getDocumentById: builder.query<DocumentResponse, number>({
      query: id => ({
        url: `documents/${id}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'Document', id }],
    }),

    getDocumentUrl: builder.query<DocumentUrlResponse, string>({
      query: s3Key => ({
        url: `documents/s3/${encodeURIComponent(s3Key)}/url`,
        method: 'GET',
      }),
    }),

    updateDocument: builder.mutation<
      { message: string; document: Document },
      { id: number; data: UpdateDocumentData }
    >({
      query: ({ id, data }) => ({
        url: `documents/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Document', id },
        'Document',
      ],
    }),

    deleteDocument: builder.mutation<{ message: string }, string>({
      query: s3Key => ({
        url: `documents/s3/${encodeURIComponent(s3Key)}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Document'],
    }),

    phoneLogin: builder.mutation<AuthResponse, PhoneLoginData>({
      query: data => ({
        url: 'auth/login/phone',
        method: 'POST',
        body: data,
      }),
      transformResponse: (response: AuthResponse) => {
        if (response?.tokens?.idToken) {
          localStorage.setItem('token', response.tokens.idToken);
        }
        return response;
      },
    }),
    getProfile: builder.query<ProfileResponse, GetProfileData>({
      query: data => ({
        url: 'users/profile',
        method: 'POST',
        body: data,
      }),
      providesTags: ['Profile'],
      keepUnusedDataFor: 300,
    }),
    createPersonalDetails: builder.mutation<AuthResponse, PersonalDetailsData>({
      query: data => ({
        url: 'personal-details/create',
        method: 'POST',
        body: data,
      }),
    }),
    getServiceProviders: builder.query<ServiceProvidersResponse, void>({
      query: () => ({
        url: 'service-providers',
        method: 'GET',
      }),
    }),
    getTimeSlots: builder.query<TimeSlotsResponse, void>({
      query: () => ({
        url: 'time-slots',
        method: 'GET',
      }),
    }),

    getProfileDetails: builder.query<ProfileDetailsResponse, void>({
      query: () => ({
        url: 'personal-details/getProfile',
        method: 'GET',
      }),
      providesTags: ['Profile'],
    }),

    updateNursePersonalDetails: builder.mutation<
      AuthResponse,
      NursePersonalDetailsData
    >({
      query: data => ({
        url: 'personal-details/updatePersonalProfile',
        method: 'PUT',
        body: data,
      }),
    }),

    updateNurseProfessionalDetails: builder.mutation<
      AuthResponse,
      NurseProfessionalDetailsData
    >({
      query: data => ({
        url: 'personal-details/updateProfessionalProfile',
        method: 'PUT',
        body: data,
      }),
    }),

    getFeedback: builder.query<GetFeedbackResponse, string>({
      query: nurseId => ({
        url: `profile/${nurseId}`,
        method: 'GET',
      }),
      providesTags: ['Feedback'],
      keepUnusedDataFor: 300,
      transformResponse: (response: unknown): GetFeedbackResponse => {
        const typedResponse = response as {
          data?: { feedback?: Feedback[]; rating_stats?: RatingStats };
          success?: boolean;
          feedback?: Feedback[];
          rating_stats?: RatingStats;
        };

        if (typedResponse?.data) {
          return {
            feedback: typedResponse.data.feedback || [],
            rating_stats: typedResponse.data.rating_stats || undefined,
            success: typedResponse.success,
          };
        }

        return {
          feedback: typedResponse?.feedback || [],
          rating_stats: typedResponse?.rating_stats || undefined,
          success: typedResponse?.success !== false,
        };
      },
      transformErrorResponse: response => {
        console.error('Feedback API Error:', response);
        return response;
      },
    }),

    createMultipleAvailabilities: builder.mutation<
      CreateMultipleAvailabilitiesResponse,
      CreateMultipleAvailabilitiesData
    >({
      query: data => ({
        url: 'availability/create',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Availability'],
    }),

    getMyAvailabilities: builder.query<
      GetMyAvailabilitiesResponse,
      { date?: string } | void
    >({
      query: (params: { date?: string } = {}) => {
        const searchParams = new URLSearchParams();
        if (params.date) searchParams.append('date', params.date);

        return {
          url: `availability/my-availabilities?${searchParams.toString()}`,
          method: 'GET',
        };
      },
      providesTags: ['Availability'],
      keepUnusedDataFor: 300,
    }),

    updateMyAvailability: builder.mutation<
      UpdateAvailabilityResponse,
      { id: number; data: UpdateAvailabilityData }
    >({
      query: ({ id, data }) => ({
        url: `availability/my-availability/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Availability'],
    }),

    deleteMyAvailability: builder.mutation<DeleteAvailabilityResponse, number>({
      query: id => ({
        url: `availability/my-availability/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Availability'],
    }),

    deleteAllMyAvailabilities: builder.mutation<
      DeleteAvailabilityResponse,
      void
    >({
      query: () => ({
        url: 'availability/my-availabilities',
        method: 'DELETE',
      }),
      invalidatesTags: ['Availability'],
    }),

    getUserDetailsForAvailability: builder.query<
      GetUserDetailsResponse,
      string
    >({
      query: nurseId => ({
        url: `availability/user/${nurseId}`,
        method: 'GET',
      }),
      keepUnusedDataFor: 600,
    }),

    getNurseAvailability: builder.query<
      GetNurseAvailabilityResponse,
      { nurseId: string; date?: string }
    >({
      query: ({ nurseId, date }) => {
        const searchParams = new URLSearchParams();
        if (date) searchParams.append('date', date);

        return {
          url: `availability/nurse/${nurseId}?${searchParams.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result, error, { nurseId }) => [
        { type: 'Availability', id: nurseId },
      ],
      keepUnusedDataFor: 300,
    }),

    getAllAvailabilities: builder.query<
      GetAllAvailabilitiesResponse,
      GetAllAvailabilitiesParams | void
    >({
      query: (params: GetAllAvailabilitiesParams = {}) => {
        const searchParams = new URLSearchParams();
        if (params.page) searchParams.append('page', params.page.toString());
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.date) searchParams.append('date', params.date);
        if (params.nurseId) searchParams.append('nurseId', params.nurseId);

        return {
          url: `availability?${searchParams.toString()}`,
          method: 'GET',
        };
      },
      providesTags: ['Availability'],
      keepUnusedDataFor: 300,
    }),

    deleteAccount: builder.mutation<AuthResponse, DeleteAccount>({
      query: data => ({
        url: 'auth/delete-account',
        method: 'POST',
        body: data,
      }),
    }),

    uploadProfileImage: builder.mutation<UploadProfileImageResponse, FormData>({
      query: formData => ({
        url: 'profile-image/upload',
        method: 'POST',
        body: formData,
      }),
      invalidatesTags: ['ProfileImage'],
    }),

    getCurrentUserProfileImage: builder.query<ProfileImageResponse, void>({
      query: () => ({
        url: 'profile-image/me',
        method: 'GET',
      }),
      providesTags: ['ProfileImage'],
      keepUnusedDataFor: 300,
    }),

    getProfileImageUrl: builder.query<ProfileImageUrlResponse, string>({
      query: s3Key => ({
        url: `profile-image/s3/${encodeURIComponent(s3Key)}/url`,
        method: 'GET',
      }),
      keepUnusedDataFor: 60,
    }),

    getProfileImageByUserId: builder.query<ProfileImageResponse, string>({
      query: userId => ({
        url: `profile-image/user/${userId}`,
        method: 'GET',
      }),
      providesTags: (result, error, userId) => [
        { type: 'ProfileImage', id: userId },
      ],
      keepUnusedDataFor: 300,
    }),

    updateProfileImage: builder.mutation<UploadProfileImageResponse, FormData>({
      query: formData => ({
        url: 'profile-image/update',
        method: 'PUT',
        body: formData,
      }),
      invalidatesTags: ['ProfileImage'],
    }),

    deleteProfileImage: builder.mutation<DeleteProfileImageResponse, void>({
      query: () => ({
        url: 'profile-image/delete',
        method: 'DELETE',
      }),
      invalidatesTags: ['ProfileImage'],
    }),

    createBankDetails: builder.mutation<BankDetailsResponse, BankDetailsData>({
      query: data => ({
        url: 'bank-details',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['BankDetails'],
    }),

    getBankDetails: builder.query<BankDetailsResponse, void>({
      query: () => ({
        url: 'bank-details',
        method: 'GET',
      }),
      providesTags: ['BankDetails'],
      keepUnusedDataFor: 300,
    }),

    updateBankDetails: builder.mutation<
      BankDetailsResponse,
      BankDetailsUpdateData
    >({
      query: data => ({
        url: 'bank-details',
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['BankDetails'],
    }),

    deleteBankDetails: builder.mutation<DeleteBankDetailsResponse, void>({
      query: () => ({
        url: 'bank-details',
        method: 'DELETE',
      }),
      invalidatesTags: ['BankDetails'],
    }),
  }),
});

export const {
  useGetExampleQuery,
  useRegisterNurseMutation,
  useVerifyOTPMutation,
  useForgotPasswordMutation,
  useChangePasswordMutation,
  useDeleteAccountMutation,
  useConfirmForgotPasswordMutation,
  useResendOTPMutation,
  useUpdateNurseLocationMutation,
  useUploadDocumentMutation,
  useListDocumentsQuery,
  useGetDocumentByIdQuery,
  useGetDocumentUrlQuery,
  useUpdateDocumentMutation,
  useDeleteDocumentMutation,
  usePhoneLoginMutation,
  useGetProfileQuery,
  useCreatePersonalDetailsMutation,
  useGetServiceProvidersQuery,
  useGetTimeSlotsQuery,
  useGetProfileDetailsQuery,
  useUpdateNursePersonalDetailsMutation,
  useUpdateNurseProfessionalDetailsMutation,
  useGetFeedbackQuery,

  useCreateMultipleAvailabilitiesMutation,
  useGetMyAvailabilitiesQuery,
  useUpdateMyAvailabilityMutation,
  useDeleteMyAvailabilityMutation,
  useDeleteAllMyAvailabilitiesMutation,
  useGetUserDetailsForAvailabilityQuery,
  useGetNurseAvailabilityQuery,
  useGetAllAvailabilitiesQuery,

  useUploadProfileImageMutation,
  useGetCurrentUserProfileImageQuery,
  useGetProfileImageUrlQuery,
  useGetProfileImageByUserIdQuery,
  useUpdateProfileImageMutation,
  useDeleteProfileImageMutation,

  useCreateBankDetailsMutation,
  useGetBankDetailsQuery,
  useUpdateBankDetailsMutation,
  useDeleteBankDetailsMutation,
} = apiSlice;
