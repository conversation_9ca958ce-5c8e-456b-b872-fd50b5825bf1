import React, { useState, useEffect, useMemo } from 'react';
import { ArrowLeft, Eye, EyeOff, Check, X } from 'lucide-react';
import { z } from 'zod';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  useChangePasswordMutation,
  useGetProfileDetailsQuery,
} from '@/store/api/apiSlice';
import { toast, toastUtils } from '@/utils/toast';

import { performLogout } from '@/utils/logout';

const passwordSchema = z
  .object({
    old_password: z.string().min(1, 'Current password is required'),
    new_password: z
      .string()
      .regex(
        /[^A-Za-z0-9]/,
        'Password must contain at least one special character'
      )
      .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
      .regex(/\d/, 'Password must contain at least one number')
      .min(8, 'Password must be at least 8 characters'),
    confirmPassword: z.string().min(1, 'Please confirm your password'),
  })
  .refine(data => data.new_password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

const PasswordManager = () => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const [changePassword, { isLoading }] = useChangePasswordMutation();
  const { data: profileDetails } = useGetProfileDetailsQuery();

  useEffect(() => {
    if (location.state?.phone_number) {
      setPhoneNumber(location.state.phone_number);
    } else if (profileDetails?.details?.phone_number) {
      setPhoneNumber(profileDetails.details.phone_number);
    }
  }, [location.state, profileDetails]);

  const handleInputChange = (field: string, value: string) => {
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
    switch (field) {
      case 'old_password':
        setOldPassword(value);
        break;
      case 'new_password':
        setNewPassword(value);
        break;
      case 'confirmPassword':
        setConfirmPassword(value);
        break;
      default:
        break;
    }
  };

  const togglePasswordVisibility = (field: string) => {
    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));
  };

  const validatePassword = (password: string) => {
    const requirements = [
      { test: password.length >= 8, text: 'At least 8 characters' },
      { test: /[A-Z]/.test(password), text: 'One uppercase letter' },
      { test: /\d/.test(password), text: 'One number' },
      { test: /[^A-Za-z0-9]/.test(password), text: 'One special character' },
    ];
    return requirements;
  };

  const handleLogout = () => {
    performLogout();
    navigate('/login');
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    setErrors({});
    try {
      if (!phoneNumber) {
        toast.error('Phone number is required. Please try again.');
        setIsSubmitting(false);
        return;
      }

      const formData = {
        old_password: oldPassword.trim(),
        new_password: newPassword.trim(),
        confirmPassword: confirmPassword.trim(),
      };

      if (formData.old_password === formData.new_password) {
        setErrors({
          new_password: 'New password cannot be the same as current password',
          confirmPassword:
            'Confirm password cannot be the same as current password',
        });
        setIsSubmitting(false);
        return;
      }

      const validationResult = passwordSchema.safeParse(formData);

      if (!validationResult.success) {
        const fieldErrors: Record<string, string> = {};
        validationResult.error.errors.forEach(err => {
          fieldErrors[err.path[0]] = err.message;
        });
        setErrors(fieldErrors);
        setIsSubmitting(false);
        return;
      }

      const apiPayload = {
        phone_number: phoneNumber.toString().trim(),
        old_password: oldPassword.trim(),
        new_password: newPassword.trim(),
      };

      const _result = await changePassword(apiPayload).unwrap();

      toast.success(
        'Your password has been updated successfully. Please log in again.'
      );

      setOldPassword('');
      setNewPassword('');
      setConfirmPassword('');

      setTimeout(() => {
        handleLogout();
      }, 2000);
    } catch (error) {
      console.error('Password change error:', error);

      const errorMessage = error?.data?.error || error?.message;
      const statusCode = error?.status;

      switch (statusCode) {
        case 404:
          toast.error('User not found. Please check your phone number.');
          break;
        case 401:
          toast.error(
            'Invalid old password. Please check your current password.'
          );
          break;
        case 400:
          if (
            errorMessage?.includes('New password does not meet requirements')
          ) {
            toast.error(
              'New password does not meet requirements. Please check the password criteria.'
            );
          } else {
            toast.error(
              errorMessage ||
                'Phone number, old password, and new password are required.'
            );
          }
          break;
        case 429:
          toast.error('Too many attempts. Please try again later.');
          break;
        case 500:
          toast.error('Failed to change password. Please try again later.');
          break;
        default:
          if (error?.name === 'NetworkError' || !navigator.onLine) {
            toastUtils.networkError();
          } else {
            toast.error('Failed to change password. Please try again.');
          }
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const passwordRequirements = validatePassword(newPassword);

  const isFormValid = useMemo(() => {
    if (
      !phoneNumber ||
      !oldPassword.trim() ||
      !newPassword.trim() ||
      !confirmPassword.trim()
    ) {
      return false;
    }

    const allRequirementsMet = passwordRequirements.every(req => req.test);

    const passwordsMatch = newPassword === confirmPassword;

    return allRequirementsMet && passwordsMatch;
  }, [
    phoneNumber,
    oldPassword,
    newPassword,
    confirmPassword,
    passwordRequirements,
  ]);

  const currentPasswordMatchesNew =
    oldPassword.trim() &&
    newPassword.trim() &&
    oldPassword.trim() === newPassword.trim();
  const currentPasswordMatchesConfirm =
    oldPassword.trim() &&
    confirmPassword.trim() &&
    oldPassword.trim() === confirmPassword.trim();

  return (
    <div className='min-h-screen flex flex-col bg-white'>
      {}
      <header className='relative w-full overflow-hidden text-white p-5 flex flex-col'>
        <div className='absolute inset-0 w-full h-full z-0 bg-fixed'>
          <img
            src='/Images/bg4.png'
            alt='Background Wallpaper'
            className='object-cover w-full'
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />
        {}
        <div className='relative z-10 h-full min-w-full flex items-center md:mb-3 top-1 '>
          <button onClick={() => navigate(-1)} className='mr-3'>
            <ArrowLeft className='h-6 w-6' />
          </button>
          <h1 className='text-xl font-semibold'>Password Manager</h1>
        </div>
      </header>

      {}
      <div className='flex-1 p-5'>
        <div className='max-w-md mx-auto'>
          <div className='bg-[#F2F2F2] rounded-lg shadow-xl border p-4'>
            <div className='space-y-6'>
              {}
              <div className='space-y-2'>
                <label
                  htmlFor='currentPassword'
                  className='block text-sm font-medium text-gray-700'
                >
                  Current Password
                </label>
                <div className='relative'>
                  <input
                    id='currentPassword'
                    type={showPasswords.current ? 'text' : 'password'}
                    value={oldPassword}
                    onChange={e =>
                      handleInputChange('old_password', e.target.value)
                    }
                    placeholder='Enter current password'
                    className={`w-full px-3 py-2 pr-10 border-2 border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1  focus:border-nursery-blue ${
                      errors.old_password ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  <button
                    type='button'
                    onClick={() => togglePasswordVisibility('current')}
                    className='absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600'
                  >
                    {showPasswords.current ? (
                      <Eye className='h-4 w-4 text-gray-500' />
                    ) : (
                      <EyeOff className='h-4 w-4 text-gray-500' />
                    )}
                  </button>
                </div>
                {errors.old_password && (
                  <p className='text-sm text-red-600'>{errors.old_password}</p>
                )}
                <div className='flex justify-end'>
                  <button
                    type='button'
                    className='text-sm text-nursery-darkBlue hover:underline'
                    onClick={() => navigate('/forgotpassword')}
                  >
                    Forgot Password?
                  </button>
                </div>
              </div>

              {}
              <div className='space-y-2'>
                <label
                  htmlFor='newPassword'
                  className='block text-sm font-medium text-gray-700'
                >
                  New Password
                </label>
                <div className='relative'>
                  <input
                    id='newPassword'
                    type={showPasswords.new ? 'text' : 'password'}
                    value={newPassword}
                    onChange={e =>
                      handleInputChange('new_password', e.target.value)
                    }
                    placeholder='Enter new password'
                    className={`w-full px-3 py-2 pr-10 border-2 border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:border-nursery-blue ${
                      errors.new_password ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  <button
                    type='button'
                    onClick={() => togglePasswordVisibility('new')}
                    className='absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600'
                  >
                    {showPasswords.new ? (
                      <Eye className='h-4 w-4 text-gray-500' />
                    ) : (
                      <EyeOff className='h-4 w-4 text-gray-500' />
                    )}
                  </button>
                </div>
                {errors.new_password && (
                  <p className='text-sm text-red-600'>{errors.new_password}</p>
                )}
                {currentPasswordMatchesNew && (
                  <p className='text-sm text-red-600'>
                    New password cannot be the same as current password
                  </p>
                )}

                {}
                {newPassword && (
                  <div className='mt-3 p-3 bg-gray-50 rounded-md'>
                    <p className='text-xs font-medium text-gray-700 mb-2'>
                      Password Requirements:
                    </p>
                    <div className='space-y-1'>
                      {passwordRequirements.map(req => (
                        <div
                          key={req.text}
                          className='flex items-center text-xs'
                        >
                          {req.test ? (
                            <Check className='h-3 w-3 text-green-500 mr-2' />
                          ) : (
                            <X className='h-3 w-3 text-red-500 mr-2' />
                          )}
                          <span
                            className={
                              req.test ? 'text-green-700' : 'text-red-700'
                            }
                          >
                            {req.text}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {}
              <div className='space-y-2'>
                <label
                  htmlFor='confirmPassword'
                  className='block text-sm font-medium text-gray-700'
                >
                  Confirm New Password
                </label>
                <div className='relative'>
                  <input
                    id='confirmPassword'
                    type={showPasswords.confirm ? 'text' : 'password'}
                    value={confirmPassword}
                    onChange={e =>
                      handleInputChange('confirmPassword', e.target.value)
                    }
                    placeholder='Confirm new password'
                    className={`w-full px-3 py-2 pr-10 border-2 border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1  focus:border-nursery-blue ${
                      errors.confirmPassword
                        ? 'border-red-500'
                        : 'border-gray-300'
                    }`}
                  />
                  <button
                    type='button'
                    onClick={() => togglePasswordVisibility('confirm')}
                    className='absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600'
                  >
                    {showPasswords.confirm ? (
                      <Eye className='h-4 w-4 text-gray-500' />
                    ) : (
                      <EyeOff className='h-4 w-4 text-gray-500' />
                    )}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className='text-sm text-red-600'>
                    {errors.confirmPassword}
                  </p>
                )}
                {currentPasswordMatchesConfirm && (
                  <p className='text-sm text-red-600'>
                    Confirm password cannot be the same as current password
                  </p>
                )}

                {}
                {newPassword && confirmPassword && (
                  <div className='flex items-center text-xs mt-1'>
                    {newPassword === confirmPassword ? (
                      <>
                        <Check className='h-3 w-3 text-green-500 mr-1' />
                        <span className='text-green-700'>Passwords match</span>
                      </>
                    ) : (
                      <>
                        <X className='h-3 w-3 text-red-500 mr-1' />
                        <span className='text-red-700'>
                          Passwords don&apos;t match
                        </span>
                      </>
                    )}
                  </div>
                )}
              </div>

              {}
              <button
                type='button'
                onClick={handleSubmit}
                disabled={
                  isSubmitting ||
                  isLoading ||
                  !isFormValid ||
                  currentPasswordMatchesNew ||
                  currentPasswordMatchesConfirm
                }
                className='w-full bg-nursery-blue text-white py-2 px-4 rounded-md hover:bg-nursery-darkBlue hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-nursery-blue focus:ring-offset-2 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed'
              >
                {isSubmitting || isLoading
                  ? 'Updating Password...'
                  : 'Update Password'}
              </button>
            </div>
          </div>

          {}
          <div className='mt-6 p-4 bg-nursery-blue bg-opacity-30 rounded-lg border border-nursery-blue'>
            <h3 className='text-sm font-medium text-nursery-darkBlue mb-2'>
              Security Tips:
            </h3>
            <ul className='text-xs text-nursery-darkBlue space-y-1'>
              <li>• Use a unique password that you don&apos;t use elsewhere</li>
              <li>• Consider using a password manager for all your accounts</li>
              <li>• Change your password regularly</li>
              <li>• Never share your password with anyone</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PasswordManager;
