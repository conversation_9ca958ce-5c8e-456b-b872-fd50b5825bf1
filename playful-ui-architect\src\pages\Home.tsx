import { Button } from '@/components/ui/button';
import {
  ChevronDown,
  CircleCheck,
  MapPinHouse,
  MessageSquare,
  User,
} from 'lucide-react';
import { useEffect, useRef, useState, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  useGetProfileQuery,
  useGetFeedbackQuery,
  useGetProfileDetailsQuery,
  useGetCurrentUserProfileImageQuery,
} from '@/store/api/apiSlice';
import { toast, toastUtils } from '@/utils/toast';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import ResponsiveLoader from '@/components/Loader';
import FeedbackCarousel from '@/components/FeedbackCarousel ';
import {
  useGetBookingsByNurseQuery,
  useUpdateBookingStatusMutation,
  useGetNurseServiceStatusesQuery,
  type ServiceStatus,
} from '../store/api/customerApiSlice';
import BookingRequests from './BookingRequest';
import UpcomingSchedule from './UpcomingSchedule';

import Footer from '@/components/Footer';
import NotificationBadge from '@/components/NotificationBadge';
import { useUnreadMessages } from '@/hooks/useUnreadMessages';

function Home() {
  const { unreadCount, refetch } = useUnreadMessages();
  const navigate = useNavigate();
  const location = useLocation();
  const [mobileView, setMobileView] = useState(false);
  const [showFullAddress, setShowFullAddress] = useState(false);
  const [feedbackRetryAttempts, setFeedbackRetryAttempts] = useState(0);
  const [bookingRetryAttempts, setBookingRetryAttempts] = useState(0);
  const [showVerified, setShowVerified] = useState(false);
  const onOpenRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (onOpenRef.current && !onOpenRef.current.contains(e.target as Node)) {
        setShowFullAddress(false);
      }
    };
    if (showFullAddress) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showFullAddress]);

  const username = location.state?.username;
  const given_name = location.state?.given_name;
  const address = location.state?.address;
  const nurse_onboarding_completed = location.state?.nurse_onboarding_completed;

  const { data: currentProfileImage } = useGetCurrentUserProfileImageQuery();
  const profileImage = currentProfileImage?.profile_image?.signed_url;

  const checkScreenSize = () => {
    setMobileView(window.innerWidth < 535);
  };

  useEffect(() => {
    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => {
      window.removeEventListener('resize', checkScreenSize);
    };
  }, []);

  const {
    data: profile,
    isLoading: profileLoading,
    error: profileError,
    refetch: refetchProfile,
  } = useGetProfileQuery(
    { username },
    {
      skip: !username,
    }
  );

  const {
    data: profileDetails,
    isLoading: profileDetailsLoading,
    error: profileDetailsError,
    refetch: refetchProfileDetails,
  } = useGetProfileDetailsQuery(undefined, {
    skip: !username,
  });

  const ProfileVerified = profileDetails?.details?.profile_verified;

  const nurseId = profileDetails?.details?.user_id;

  if (nurseId) {
    localStorage.setItem('userId', nurseId);
  }

  const refetchUnreadCount = useCallback(() => {
    if (localStorage.getItem('userId')) {
      refetch();
    }
  }, [refetch]);

  useEffect(() => {
    const handleFocus = () => {
      refetchUnreadCount();
    };
    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [refetchUnreadCount]);

  const {
    data: bookingResponse,
    isLoading: bookingLoading,
    error: bookingError,
    refetch: refetchBookings,
  } = useGetBookingsByNurseQuery(nurseId, {
    skip: !nurseId,
    pollingInterval: 60000,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const [updateBookingStatus, { isLoading: updatingStatus }] =
    useUpdateBookingStatusMutation();

  const {
    data: serviceStatusResponse,
    isLoading: _serviceStatusLoading,
    error: _serviceStatusError,
  } = useGetNurseServiceStatusesQuery(nurseId, {
    skip: !nurseId,
    pollingInterval: 30000,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const [serviceStatus, setServiceStatus] = useState<
    Record<number, ServiceStatus>
  >({});
  useEffect(() => {
    if (serviceStatusResponse?.success && serviceStatusResponse?.data) {
      const statusMap: Record<number, ServiceStatus> = {};
      const dataArray = Array.isArray(serviceStatusResponse.data)
        ? serviceStatusResponse.data
        : [serviceStatusResponse.data];

      dataArray.forEach((status: ServiceStatus) => {
        statusMap[status.booking_id] = status;
      });
      setServiceStatus(statusMap);
    }
  }, [serviceStatusResponse]);

  const {
    data: feedbackData,
    isLoading: feedbackLoading,
    error: feedbackError,
    refetch: refetchFeedback,
  } = useGetFeedbackQuery(nurseId, {
    skip: !nurseId || profileDetailsLoading,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
  });

  const handleBookingStatus = async (
    booking_id: number,
    status: 'Accepted' | 'Declined',
    custom_reason?: string
  ) => {
    try {
      const requestData: {
        booking_id: number;
        booking_status:
          | 'Pending'
          | 'Accepted'
          | 'Declined'
          | 'Cancelled'
          | 'Completed';
        custom_reason?: string;
      } = {
        booking_id,
        booking_status: status,
      };

      if (status === 'Declined' && custom_reason) {
        requestData.custom_reason = custom_reason;
      }

      await updateBookingStatus(requestData).unwrap();

      toastUtils.successAlt(
        `Booking ${status === 'Accepted' ? 'accepted' : 'declined'} successfully!`
      );

      refetchBookings();
    } catch (error: unknown) {
      console.error('Failed to update booking status:', error);

      let errorMessage = 'Failed to update booking status';
      if (
        error &&
        typeof error === 'object' &&
        'status' in error &&
        error.status === 'FETCH_ERROR'
      ) {
        errorMessage =
          'Network error. Please check your connection and try again.';
      } else if (
        error &&
        typeof error === 'object' &&
        'data' in error &&
        error.data &&
        typeof error.data === 'object' &&
        'error' in error.data
      ) {
        errorMessage = (error.data as { error: string }).error;
      }

      toast.error(errorMessage);
    }
  };

  const handleBookingRetry = () => {
    setBookingRetryAttempts(0);
    refetchBookings();
    toastUtils.retryInfo('load bookings');
  };

  useEffect(() => {
    if (!username) {
      navigate('/login');
      return;
    }
    if (profileError) {
      console.error('Profile error:', profileError);
      toast.error('Failed to load profile');
      return;
    }
    if (!profileLoading && profile) {
      const isOnboardingCompleted =
        profile.nurse_onboard_complete || nurse_onboarding_completed;

      if (!isOnboardingCompleted) {
        navigate('/onboarding', {
          state: { given_name: profileDetails?.details?.given_name },
        });
      } else if (isOnboardingCompleted && !profile.nurse_set_location) {
        navigate('/location');
        return;
      }
    }
  }, [
    profile,
    profileLoading,
    profileError,
    username,
    navigate,
    profileDetails,
    nurse_onboarding_completed,
  ]);

  useEffect(() => {
    if (bookingError) {
      console.error('Booking fetch error:', bookingError);

      if (bookingRetryAttempts < 3) {
        const errorMessage =
          bookingError &&
          'status' in bookingError &&
          bookingError.status === 'FETCH_ERROR'
            ? `Network error loading bookings (Attempt ${bookingRetryAttempts + 1})`
            : `Failed to load bookings (Attempt ${bookingRetryAttempts + 1})`;

        toast.error(errorMessage);
      }
    }
  }, [bookingError, bookingRetryAttempts]);

  useEffect(() => {
    if (bookingError && nurseId && bookingRetryAttempts < 2) {
      const retryTimer = setTimeout(
        () => {
          setBookingRetryAttempts(prev => prev + 1);
          refetchBookings();
        },
        3000 * (bookingRetryAttempts + 1)
      );

      return () => clearTimeout(retryTimer);
    }
  }, [bookingError, nurseId, bookingRetryAttempts, refetchBookings]);

  useEffect(() => {
    if (feedbackError) {
      console.error('Feedback fetch error:', feedbackError);

      if (feedbackRetryAttempts < 3) {
        toast.error(
          `Failed to load feedback data (Attempt ${feedbackRetryAttempts + 1})`
        );
      }
    }
  }, [feedbackError, feedbackRetryAttempts]);

  useEffect(() => {
    if (feedbackError && nurseId && feedbackRetryAttempts < 2) {
      const retryTimer = setTimeout(
        () => {
          setFeedbackRetryAttempts(prev => prev + 1);
          refetchFeedback();
        },
        2000 * (feedbackRetryAttempts + 1)
      );

      return () => clearTimeout(retryTimer);
    }
  }, [feedbackError, nurseId, feedbackRetryAttempts, refetchFeedback]);

  useEffect(() => {
    if (nurseId && !feedbackLoading && !feedbackData && !feedbackError) {
      refetchFeedback();
    }
  }, [nurseId, feedbackLoading, feedbackData, feedbackError, refetchFeedback]);

  const handleManualRetry = () => {
    setFeedbackRetryAttempts(0);
    refetchFeedback();
    toastUtils.retryInfo('load feedback');
  };

  if (profileLoading || profileDetailsLoading) {
    return (
      <div>
        <ResponsiveLoader />
      </div>
    );
  }

  if (profileError || profileDetailsError) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='text-center'>
          <div className='text-red-600 text-lg mb-4'>Error loading profile</div>
          <Button
            onClick={() => {
              refetchProfile();
              refetchProfileDetails();
            }}
            className='px-4 py-2 bg-nursery-darkBlue text-white rounded-md hover:bg-opacity-80'
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }

  const getFeedbacks = () => {
    if (feedbackData?.feedback && Array.isArray(feedbackData.feedback)) {
      return feedbackData.feedback;
    }
    if (Array.isArray(feedbackData)) {
      return feedbackData;
    }
    return [];
  };

  const feedbacks = getFeedbacks();
  const totalFeedbacks = feedbacks.length;

  const displayName =
    given_name || profileDetails?.details?.given_name || 'User';
  const displayAddress =
    address || profileDetails?.details?.address || 'Address not available';

  return (
    <>
      <div>
        <header className='w-full'>
          <div
            className='flex flex-row py-6 md:px-7 px-3 items-center max-w-full bg-white justify-between'
            style={{
              backgroundImage: `url(/Images/bg4.png)`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
            }}
          >
            <div className='w-full mx-auto md:w-10/12 flex justify-between items-center'>
              <div>
                {mobileView ? (
                  <img
                    src='/Images/whitefavicon.svg'
                    alt='Company Logo'
                    className='logo logo-mobile w-[45px] object-cover'
                  />
                ) : (
                  <img
                    src='/Images/Logo.svg'
                    alt='Company Logo'
                    className='logo logo-desktop w-[155px] object-cover'
                  />
                )}
              </div>
              <div className='flex flex-col md:flex-row md:items-center gap-2'>
                <div className='relative'>
                  <h2 className='md:text-lg text-sm font-bold text-white flex flex-row gap-1 items-center'>
                    <span className='text-white'>
                      <MapPinHouse className='w-4 h-4 md:w-5 md:h-5' />
                    </span>
                    HOME
                  </h2>
                  <div
                    className='flex flex-row items-end cursor-pointer text-white'
                    onClick={() => setShowFullAddress(!showFullAddress)}
                  >
                    <p className='md:text-sm text-sm font-medium truncate max-w-[200px] md:max-w-[250px]'>
                      {displayAddress !== 'Address not available'
                        ? displayAddress.split(',').slice(0, 3).join(',') +
                          '...'
                        : displayAddress}
                    </p>
                    <span>
                      <ChevronDown
                        size={16}
                        strokeWidth={'2.5px'}
                        className={`transition-transform ${showFullAddress ? 'rotate-180' : ''}`}
                      />
                    </span>
                  </div>

                  {showFullAddress && (
                    <div
                      ref={onOpenRef}
                      className='absolute mt-1 rounded-md z-10 max-w-xs right-0'
                    >
                      <p className='text-sm p-2 bg-white text-gray-800 rounded-lg shadow-lg'>
                        {displayAddress}
                      </p>
                    </div>
                  )}
                </div>
                <div className='hidden md:flex md:items-center gap-6'>
                  <div className='cursor-pointer p-2'>
                    <div className='relative'>
                      <MessageSquare
                        className='h-7 w-7 text-white'
                        onClick={() => navigate('/chat')}
                      />
                      <NotificationBadge count={unreadCount} />
                    </div>
                  </div>
                  <button
                    className='cursor-pointer p-[2px] bg-[#F2F2F2] rounded-full'
                    onClick={() => navigate('/profile')}
                  >
                    <Avatar
                      className={`${profileImage ? 'h-12 w-12' : 'p-1'} bg-white border-1 border-white shadow-lg`}
                    >
                      {profileImage ? (
                        <AvatarImage
                          src={profileImage}
                          alt='Profile'
                          className='object-cover'
                        />
                      ) : (
                        <AvatarFallback className='bg-white text-nursery-blue text-2xl'>
                          <User className=' text-nursery-blue' />
                        </AvatarFallback>
                      )}
                    </Avatar>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </header>

        <main className='flex-1 overflow-auto bg-white'>
          <div className='flex flex-1 flex-col p-4 mx-auto w-11/12 md:w-10/12'>
            <div
              className={`flex gap-1 ${ProfileVerified ? ' items-center flex-row' : 'items-start flex-col'}`}
            >
              <h1 className='text-xl font-bold text-gray-800 mb-1'>
                Welcome! <em className='text-nursery-blue'>{displayName}</em>
              </h1>
              {ProfileVerified ? (
                <>
                  <button
                    title='Profile Verified'
                    onClick={() => {
                      setShowVerified(true);
                      setTimeout(() => setShowVerified(false), 2000);
                    }}
                    className='relative'
                  >
                    <CircleCheck className='w-5 h-5 text-[#00A912]' />
                    {showVerified && (
                      <span className='absolute -left-5 -top-3 translate-x-1/2 mt-2 px-2 py-1  bg-nursery-blue text-white text-xs rounded shadow z-10 whitespace-nowrap'>
                        Profile Verified
                      </span>
                    )}
                  </button>
                </>
              ) : (
                <p className='inline-block text-base px-4 py-1 bg-[#f09e22] text-white rounded-full items-center'>
                  Profile is Under Review
                </p>
              )}
            </div>

            <BookingRequests
              bookingResponse={bookingResponse}
              bookingLoading={bookingLoading}
              bookingError={bookingError}
              bookingRetryAttempts={bookingRetryAttempts}
              handleBookingRetry={handleBookingRetry}
              handleBookingStatus={handleBookingStatus}
              updatingStatus={updatingStatus}
            />

            <UpcomingSchedule
              bookingResponse={bookingResponse}
              handleBookingStatus={handleBookingStatus}
              updatingStatus={updatingStatus}
              serviceStatus={serviceStatus}
            />

            <div className='pt-6 pb-2'>
              <div className='flex flex-row items-center justify-between pb-3'>
                <h3 className='text-xl font-semibold text-gray-800'>
                  Latest Feedback for You
                </h3>
                {totalFeedbacks > 0 && (
                  <div className='flex items-center gap-2 bg-nursery-blue text-gray-800 px-3 py-1 rounded-full '>
                    <span className='text-white text-sm font-semibold'>
                      Feedbacks
                    </span>
                    <span className='text-white text-sm font-semibold'>
                      ({totalFeedbacks})
                    </span>
                  </div>
                )}
              </div>

              {feedbackLoading ? (
                <div className='bg-[#F2F2F2] p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2 min-h-[280px]'>
                  <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-nursery-darkBlue'></div>
                  <p className='text-base font-medium text-slate-500'>
                    Loading feedbacks...
                  </p>
                </div>
              ) : feedbackError ? (
                <div className='bg-[#F2F2F2] p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2 min-h-[280px]'>
                  <img
                    src='/Images/stars.svg'
                    alt='Error loading feedbacks'
                    className='p-2 w-20'
                  />
                  <p className='text-base font-medium text-red-500'>
                    Failed to load feedbacks
                  </p>
                  <p className='text-xs text-gray-400 mb-2'>
                    Attempts: {feedbackRetryAttempts + 1}/3
                  </p>
                  <Button
                    onClick={handleManualRetry}
                    className='mt-2 px-4 py-2 bg-nursery-darkBlue text-white rounded-md hover:bg-opacity-80'
                    disabled={feedbackLoading}
                  >
                    {feedbackLoading ? 'Retrying...' : 'Retry'}
                  </Button>
                </div>
              ) : !nurseId ? (
                <div className='bg-[#F2F2F2] p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2 min-h-[280px]'>
                  <img
                    src='/Images/stars.svg'
                    alt='No User ID'
                    className='p-2 w-20'
                  />
                  <p className='text-base font-medium text-orange-500'>
                    Unable to load feedbacks
                  </p>
                  <p className='text-xs text-gray-400'>User ID not available</p>
                </div>
              ) : (
                <FeedbackCarousel feedbacks={feedbacks} />
              )}
            </div>
          </div>
        </main>
        <Footer />
      </div>
    </>
  );
}

export default Home;
